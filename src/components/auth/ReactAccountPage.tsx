import React, { useEffect, useState } from "react";
import { useUser } from "../../contexts/UserContext";
import { fetchCustomerBookings } from "../../utils/store/bookings";
import type { Booking as BaseBooking } from "../../utils/store/bookings";
import AccountLayout from "../account/AccountLayout";
import AccountDashboard from "../account/AccountDashboard";
import MyTripsTable from "../account/MyTripsTable";

// Extended Booking interface with all the properties we need
interface Booking extends BaseBooking {
  guest_name?: string;
  guest_email?: string;
  guest_phone?: string;
  payment_status?: string;
  check_in_time?: string;
  check_out_time?: string;
  number_of_guests?: number;
  updated_at?: string;
  room_config_name?: string;
}

interface UserData {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone?: string;
  company_name?: string | null;
  created_at?: string;
}

// Define the shape of our user context
interface UserContext {
  user: UserData | null;
  isAuthenticated: boolean;
  loading: boolean;
  logout: () => Promise<void>;
}

// Pagination interface
interface PaginationState {
  currentPage: number;
  totalPages: number;
  itemsPerPage: number;
}

const ReactAccountPage: React.FC = () => {
  const [error, setError] = useState<string | null>(null);
  const [userData, setUserData] = useState<UserData | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [bookingsLoading, setBookingsLoading] = useState<boolean>(true);
  const [showEditProfile, setShowEditProfile] = useState<boolean>(false);
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [showBookingModal, setShowBookingModal] = useState<boolean>(false);
  const [activeSection, setActiveSection] = useState<string>("dashboard");

  // Pagination state
  const [pagination, setPagination] = useState<PaginationState>({
    currentPage: 1,
    totalPages: 1,
    itemsPerPage: 3,
  });

  // Get user context outside of useEffect
  let userContext: UserContext | null = null;
  try {
    userContext = useUser();
  } catch (error) {
    // Context not available, will use token-based auth
  }

  // Authentication setup
  useEffect(() => {
    const initAuth = async () => {
      try {
        // First try to use UserContext if available
        if (userContext && userContext.user) {
          setUserData(userContext.user);
          setIsAuthenticated(true);
          setLoading(false);
          return;
        }

        // Fallback to token-based auth
        const token = localStorage.getItem("auth_token");
        if (token) {
          await fetchUserData(token);
        } else {
          redirectToLogin();
        }
      } catch (error) {
        setError("Authentication failed");
        setLoading(false);
      }
    };

    initAuth();
  }, []);

  // Load bookings data with pagination
  useEffect(() => {
    const fetchBookings = async () => {
      try {
        setBookingsLoading(true);

        // Calculate offset based on current page and items per page
        const offset = (pagination.currentPage - 1) * pagination.itemsPerPage;

        // Fetch bookings from API
        const result = await fetchCustomerBookings({
          limit: pagination.itemsPerPage,
          offset: offset,
        });

        // Calculate total pages
        const totalPages = Math.ceil(result.count / pagination.itemsPerPage);
        setPagination((prev) => ({
          ...prev,
          totalPages: totalPages || 1, // Ensure at least 1 page
        }));

        // Set bookings data
        if (Array.isArray(result.bookings)) {
          setBookings(result.bookings);
        } else {
          console.error("Unexpected bookings data format:", result);
          setBookings([]);
        }
      } catch (error) {
        console.error("Error fetching bookings:", error);
        // Fallback to empty array if API fails
        setBookings([]);
        setPagination((prev) => ({
          ...prev,
          totalPages: 1,
        }));
      } finally {
        setBookingsLoading(false);
      }
    };

    if (isAuthenticated) {
      // Check if we have an auth token before trying to fetch bookings
      const authToken = localStorage.getItem("auth_token");
      if (authToken) {
        fetchBookings();
      } else {
        console.warn("No auth token found, skipping bookings fetch");
        setBookingsLoading(false);
      }
    }
  }, [isAuthenticated, pagination.currentPage, pagination.itemsPerPage]);

  // Handle page change
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= pagination.totalPages) {
      setPagination((prev) => ({
        ...prev,
        currentPage: newPage,
      }));
    }
  };

  // Fetch user data from API
  const fetchUserData = async (token: string) => {
    try {
      setLoading(true);
      const apiKey = import.meta.env.PUBLIC_BACKEND_API_KEY || "";
      const response = await fetch(
        `${import.meta.env.PUBLIC_BACKEND_URL}/store/customers/me`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
            "x-publishable-api-key": apiKey,
          },
          credentials: "include",
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch user data");
      }

      const data = await response.json();
      if (data.customer) {
        setUserData(data.customer);
        setIsAuthenticated(true);
      } else {
        redirectToLogin();
      }
    } catch (error) {
      setError("Failed to load account information");
    } finally {
      setLoading(false);
    }
  };

  // Handle logout
  const handleLogout = async () => {
    try {
      if (userContext && userContext.logout) {
        await userContext.logout();
      } else {
        // Fallback to manual token removal if context not available
        localStorage.removeItem("auth_token");
      }
      window.location.href = "/";
    } catch (error) {
      console.error("Logout error:", error);
      // Still redirect even if there's an error
      window.location.href = "/";
    }
  };

  // Redirect to login page
  const redirectToLogin = () => {
    setError("Please log in to access your account");
    setLoading(false);
    setTimeout(() => {
      window.location.href = "/login";
    }, 1500);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[70vh]">
        <div className="text-center">
          <div className="inline-block w-12 h-12 border-4 border-[#285DA6]/30 border-t-[#285DA6] rounded-full animate-spin mb-4"></div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="bg-red-50 border border-red-100 text-red-700 p-4 rounded-lg max-w-md">
          <div className="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-2 text-red-500"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
            {error}
          </div>
        </div>
      </div>
    );
  }

  // Not authenticated state
  if (!isAuthenticated || !userData) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <p className="text-gray-600">Redirecting to login page...</p>
        </div>
      </div>
    );
  }

  // Handle edit profile toggle
  const toggleEditProfile = () => {
    setShowEditProfile(!showEditProfile);
  };

  // Handle profile update
  const handleProfileUpdate = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, this would send the updated data to the API
    setShowEditProfile(false);
    // Show success message or handle errors
  };

  // Handle opening booking details modal
  const openBookingModal = (booking: Booking) => {
    setSelectedBooking(booking);
    setShowBookingModal(true);
  };

  // Handle closing booking details modal
  const closeBookingModal = () => {
    setShowBookingModal(false);
    setSelectedBooking(null);
  };

  // SVG icons
  const Icons = {
    user: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
        <circle cx="12" cy="7" r="4"></circle>
      </svg>
    ),
    close: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <line x1="18" y1="6" x2="6" y2="18"></line>
        <line x1="6" y1="6" x2="18" y2="18"></line>
      </svg>
    ),
    email: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
        <polyline points="22,6 12,13 2,6"></polyline>
      </svg>
    ),
    phone: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
      </svg>
    ),
    password: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
        <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
      </svg>
    ),
    signOut: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
        <polyline points="16 17 21 12 16 7"></polyline>
        <line x1="21" y1="12" x2="9" y2="12"></line>
      </svg>
    ),
    calendar: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
        <line x1="16" y1="2" x2="16" y2="6"></line>
        <line x1="8" y1="2" x2="8" y2="6"></line>
        <line x1="3" y1="10" x2="21" y2="10"></line>
      </svg>
    ),
    edit: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
      </svg>
    ),
    chevronLeft: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <polyline points="15 18 9 12 15 6"></polyline>
      </svg>
    ),
    chevronRight: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <polyline points="9 18 15 12 9 6"></polyline>
      </svg>
    ),
  };

  // Format date for display in a more user-friendly way
  const formatDisplayDate = (dateString: string) => {
    const date = new Date(dateString);

    // Get day of week, day, month and year
    const dayOfWeek = date.toLocaleDateString("en-US", { weekday: "long" });
    const day = date.getDate();
    const month = date.toLocaleDateString("en-US", { month: "long" });
    const year = date.getFullYear();

    // Add ordinal suffix to day (1st, 2nd, 3rd, etc.)
    const ordinalSuffix = (day: number): string => {
      if (day > 3 && day < 21) return "th";
      switch (day % 10) {
        case 1:
          return "st";
        case 2:
          return "nd";
        case 3:
          return "rd";
        default:
          return "th";
      }
    };

    // Return formatted date: "Monday, 15th January 2024"
    return `${dayOfWeek}, ${day}${ordinalSuffix(day)} ${month} ${year}`;
  };

  // Format date and time for display in a more user-friendly way
  const formatDisplayDateTime = (dateString: string) => {
    if (!dateString) return "Not available";

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return "Invalid date";

    // Get the date part
    const formattedDate = formatDisplayDate(dateString);

    // Get the time part
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const ampm = hours >= 12 ? "PM" : "AM";
    const formattedHours = hours % 12 || 12; // Convert 0 to 12 for 12 AM
    const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;

    // Return formatted date and time: "Monday, 15th January 2024 at 2:30 PM"
    return `${formattedDate} at ${formattedHours}:${formattedMinutes} ${ampm}`;
  };

  // Get status badge based on booking status
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "confirmed":
        return (
          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-green-50 to-green-100 text-green-700 border border-green-200 shadow-sm">
            <div className="w-4 h-4 rounded-full bg-green-500 flex items-center justify-center mr-1.5">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="10"
                height="10"
                viewBox="0 0 24 24"
                fill="none"
                stroke="white"
                strokeWidth="3"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <polyline points="20 6 9 17 4 12"></polyline>
              </svg>
            </div>
            Confirmed
          </span>
        );
      case "pending":
        return (
          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-amber-50 to-amber-100 text-amber-700 border border-amber-200 shadow-sm">
            <div className="w-4 h-4 rounded-full bg-amber-500 flex items-center justify-center mr-1.5">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="10"
                height="10"
                viewBox="0 0 24 24"
                fill="none"
                stroke="white"
                strokeWidth="3"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <line x1="12" y1="8" x2="12" y2="12"></line>
                <line x1="12" y1="16" x2="12.01" y2="16"></line>
              </svg>
            </div>
            Pending
          </span>
        );
      case "cancelled":
        return (
          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-red-50 to-red-100 text-red-700 border border-red-200 shadow-sm">
            <div className="w-4 h-4 rounded-full bg-red-500 flex items-center justify-center mr-1.5">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="10"
                height="10"
                viewBox="0 0 24 24"
                fill="none"
                stroke="white"
                strokeWidth="3"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </div>
            Cancelled
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-gray-50 to-gray-100 text-gray-700 border border-gray-200 shadow-sm">
            <div className="w-4 h-4 rounded-full bg-gray-500 flex items-center justify-center mr-1.5">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="10"
                height="10"
                viewBox="0 0 24 24"
                fill="none"
                stroke="white"
                strokeWidth="3"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <circle cx="12" cy="12" r="2"></circle>
              </svg>
            </div>
            Unknown
          </span>
        );
    }
  };

  // Edit Profile Form Component
  const EditProfileForm = () => (
    <form onSubmit={handleProfileUpdate} className="space-y-4">
      <div>
        <label
          htmlFor="first_name"
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          First Name
        </label>
        <input
          type="text"
          id="first_name"
          name="first_name"
          defaultValue={userData?.first_name || ""}
          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#285DA6] focus:border-[#285DA6] transition-colors"
        />
      </div>

      <div>
        <label
          htmlFor="last_name"
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          Last Name
        </label>
        <input
          type="text"
          id="last_name"
          name="last_name"
          defaultValue={userData?.last_name || ""}
          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#285DA6] focus:border-[#285DA6] transition-colors"
        />
      </div>

      <div>
        <label
          htmlFor="email"
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          Email Address
        </label>
        <input
          type="email"
          id="email"
          name="email"
          defaultValue={userData?.email || ""}
          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#285DA6] focus:border-[#285DA6] transition-colors"
          disabled
        />
        <p className="text-xs text-gray-500 mt-1">Email cannot be changed</p>
      </div>

      <div>
        <label
          htmlFor="phone"
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          Phone Number
        </label>
        <input
          type="tel"
          id="phone"
          name="phone"
          defaultValue={userData?.phone || ""}
          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#285DA6] focus:border-[#285DA6] transition-colors"
        />
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <button
          type="button"
          onClick={() => setShowEditProfile(false)}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-[#285DA6] text-white rounded-md hover:bg-[#1A3A6E] transition-colors"
        >
          Save Changes
        </button>
      </div>
    </form>
  );

  // Booking Modal Component
  const BookingModal = () => {
    if (!selectedBooking || !showBookingModal) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-[300] p-4 backdrop-blur-sm animate-fadeIn">
        <div
          className="bg-white rounded-xl shadow-2xl max-w-3xl w-full max-h-[90vh] overflow-y-auto animate-scaleIn"
          style={{
            boxShadow:
              "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
          }}
        >
          {/* Header with gradient background */}
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-[#1A3A6E] to-[#285DA6] opacity-90 rounded-t-xl"></div>
            <div className="relative flex justify-between items-center p-6">
              <h3 className="text-xl font-baskervville text-white flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="mr-2"
                >
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                  <line x1="16" y1="2" x2="16" y2="6"></line>
                  <line x1="8" y1="2" x2="8" y2="6"></line>
                  <line x1="3" y1="10" x2="21" y2="10"></line>
                </svg>
                Booking Details
              </h3>
              <button
                onClick={closeBookingModal}
                className="text-white/80 hover:text-white transition-colors rounded-full p-1 hover:bg-white/10"
                aria-label="Close modal"
              >
                {Icons.close}
              </button>
            </div>
          </div>

          {/* Hotel name and image banner */}
          <div className="relative bg-[#F8FAFC] p-6 border-b border-gray-100">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
              <div>
                <h4 className="text-xl font-medium text-gray-800 mb-1">
                  {selectedBooking.hotel_name}
                </h4>
                <div className="flex items-center">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-50 text-blue-700 mr-2">
                    {selectedBooking.room_config_name ||
                      selectedBooking.room_type}
                  </span>
                  {/* {getStatusBadge(selectedBooking.status)} */}
                </div>
              </div>
              <div className="mt-4 md:mt-0">
                <div className="text-2xl font-medium text-[#285DA6]">
                  {selectedBooking.currency_code.toUpperCase()}{" "}
                  {selectedBooking.total_amount.toFixed(2)}
                </div>
                <div className="text-xs text-right text-gray-500 capitalize">
                  Payment: {selectedBooking.payment_status || "N/A"}
                </div>
              </div>
            </div>
          </div>

          <div className="p-6">
            {/* Dates with fancy styling */}
            <div className="bg-gradient-to-r from-[#F0F7FF] to-[#E6F0FF] rounded-xl overflow-hidden mb-8">
              <div className="grid grid-cols-1 md:grid-cols-2 divide-y md:divide-y-0 md:divide-x divide-blue-100">
                <div className="p-5">
                  <div className="flex items-start">
                    <div className="w-10 h-10 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <rect
                          x="3"
                          y="4"
                          width="18"
                          height="18"
                          rx="2"
                          ry="2"
                        ></rect>
                        <line x1="16" y1="2" x2="16" y2="6"></line>
                        <line x1="8" y1="2" x2="8" y2="6"></line>
                        <line x1="3" y1="10" x2="21" y2="10"></line>
                        <circle cx="8" cy="14" r="2"></circle>
                      </svg>
                    </div>
                    <div>
                      <p className="text-xs text-[#285DA6] uppercase tracking-wider font-medium mb-1">
                        Check-in
                      </p>
                      <p className="text-lg font-medium text-gray-800">
                        {formatDisplayDate(selectedBooking.check_in_date)}
                      </p>
                      <p className="text-sm text-gray-500 flex items-center mt-1">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="14"
                          height="14"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="mr-1"
                        >
                          <circle cx="12" cy="12" r="10"></circle>
                          <polyline points="12 6 12 12 16 14"></polyline>
                        </svg>
                        After {selectedBooking.check_in_time || "14:00"}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="p-5">
                  <div className="flex items-start">
                    <div className="w-10 h-10 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <rect
                          x="3"
                          y="4"
                          width="18"
                          height="18"
                          rx="2"
                          ry="2"
                        ></rect>
                        <line x1="16" y1="2" x2="16" y2="6"></line>
                        <line x1="8" y1="2" x2="8" y2="6"></line>
                        <line x1="3" y1="10" x2="21" y2="10"></line>
                        <circle cx="16" cy="16" r="2"></circle>
                      </svg>
                    </div>
                    <div>
                      <p className="text-xs text-[#285DA6] uppercase tracking-wider font-medium mb-1">
                        Check-out
                      </p>
                      <p className="text-lg font-medium text-gray-800">
                        {formatDisplayDate(selectedBooking.check_out_date)}
                      </p>
                      <p className="text-sm text-gray-500 flex items-center mt-1">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="14"
                          height="14"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="mr-1"
                        >
                          <circle cx="12" cy="12" r="10"></circle>
                          <polyline points="12 6 12 12 16 14"></polyline>
                        </svg>
                        Before {selectedBooking.check_out_time || "11:00"}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Guest Info with fancy styling */}
            <div className="mb-8">
              <h5 className="text-sm font-medium text-gray-700 uppercase tracking-wider mb-3 flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="mr-2"
                >
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                  <circle cx="12" cy="7" r="4"></circle>
                </svg>
                Guest Information
              </h5>
              <div className="bg-white p-5 rounded-xl border border-gray-200 shadow-sm">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                        <circle cx="12" cy="7" r="4"></circle>
                      </svg>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500 uppercase tracking-wider">
                        Guest Name
                      </p>
                      <p className="font-medium">
                        {selectedBooking.guest_name || "Not provided"}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                        <polyline points="22,6 12,13 2,6"></polyline>
                      </svg>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500 uppercase tracking-wider">
                        Email
                      </p>
                      <p className="font-medium">
                        {selectedBooking.guest_email || "Not provided"}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                      </svg>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500 uppercase tracking-wider">
                        Phone
                      </p>
                      <p className="font-medium">
                        {selectedBooking.guest_phone || "Not provided"}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                        <circle cx="9" cy="7" r="4"></circle>
                        <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                      </svg>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500 uppercase tracking-wider">
                        Number of Guests
                      </p>
                      <p className="font-medium">
                        {selectedBooking.number_of_guests || 1}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Reservation Information with fancy styling */}
            <div className="bg-gray-50 p-5 rounded-xl border border-gray-100">
              <h5 className="text-sm font-medium text-gray-700 uppercase tracking-wider mb-3 flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="mr-2"
                >
                  <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                  <line x1="3" y1="9" x2="21" y2="9"></line>
                  <line x1="9" y1="21" x2="9" y2="9"></line>
                </svg>
                Booking Information
              </h5>
              <div className="space-y-2 text-sm">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between py-2 border-b border-gray-100">
                  <span className="text-gray-500">Created</span>
                  <span className="font-medium text-gray-800">
                    {formatDisplayDateTime(selectedBooking.created_at)}
                  </span>
                </div>
                {selectedBooking.updated_at && (
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between py-2">
                    <span className="text-gray-500">Last Updated</span>
                    <span className="font-medium text-gray-800">
                      {formatDisplayDateTime(selectedBooking.updated_at)}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* <div className="p-6 border-t border-gray-200 flex justify-end bg-gray-50">
            <button
              onClick={closeBookingModal}
              className="px-6 py-2.5 bg-[#285DA6] text-white rounded-lg hover:bg-[#1A3A6E] transition-colors shadow-sm flex items-center"
            >
              Close
            </button>
          </div> */}
        </div>
      </div>
    );
  };

  // Pagination Component
  const PaginationComponent = () => {
    const pageNumbers = [];
    const maxPagesToShow = 5;

    if (pagination.totalPages <= maxPagesToShow) {
      // If we have 5 or fewer pages, show all of them
      for (let i = 1; i <= pagination.totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      // Always include first page
      pageNumbers.push(1);

      // Calculate start and end of page numbers to show
      let start = Math.max(2, pagination.currentPage - 1);
      let end = Math.min(pagination.totalPages - 1, pagination.currentPage + 1);

      // Adjust if we're at the beginning
      if (pagination.currentPage <= 2) {
        end = Math.min(pagination.totalPages - 1, 4);
      }

      // Adjust if we're at the end
      if (pagination.currentPage >= pagination.totalPages - 1) {
        start = Math.max(2, pagination.totalPages - 3);
      }

      // Add ellipsis if needed before middle pages
      if (start > 2) {
        pageNumbers.push("...");
      }

      // Add middle pages
      for (let i = start; i <= end; i++) {
        pageNumbers.push(i);
      }

      // Add ellipsis if needed after middle pages
      if (end < pagination.totalPages - 1) {
        pageNumbers.push("...");
      }

      // Always include last page
      pageNumbers.push(pagination.totalPages);
    }

    return (
      <div className="flex flex-col items-center mt-8">
        <div className="flex justify-center items-center space-x-2">
          {/* Previous Page Button */}
          {pagination.currentPage > 1 ? (
            <button
              onClick={() => handlePageChange(pagination.currentPage - 1)}
              className="flex items-center justify-center w-10 h-10 rounded-md border border-[#285DA6]/30 text-[#285DA6] hover:bg-[#285DA6]/5 hover:border-[#285DA6]/50 transition-colors"
              aria-label="Previous page"
            >
              {Icons.chevronLeft}
            </button>
          ) : (
            <span
              className="flex items-center justify-center w-10 h-10 rounded-md border border-gray-200 text-gray-300 cursor-not-allowed"
              aria-label="Previous page (disabled)"
            >
              {Icons.chevronLeft}
            </span>
          )}

          {/* Page Numbers */}
          {pageNumbers.map((pageNumber, index) => {
            if (pageNumber === "...") {
              return (
                <span
                  key={`ellipsis-${index}`}
                  className="flex items-center justify-center w-10 h-10 text-gray-500 font-karla"
                >
                  ...
                </span>
              );
            }

            return pageNumber === pagination.currentPage ? (
              <span
                key={`page-${pageNumber}`}
                className="flex items-center justify-center w-10 h-10 rounded-md bg-[#285DA6] text-white font-karla font-medium"
                aria-current="page"
              >
                {pageNumber}
              </span>
            ) : (
              <button
                key={`page-${pageNumber}`}
                onClick={() => handlePageChange(pageNumber as number)}
                className="flex items-center justify-center w-10 h-10 rounded-md border border-[#285DA6]/30 text-[#285DA6] hover:bg-[#285DA6]/5 hover:border-[#285DA6]/50 transition-colors font-karla"
              >
                {pageNumber}
              </button>
            );
          })}

          {/* Next Page Button */}
          {pagination.currentPage < pagination.totalPages ? (
            <button
              onClick={() => handlePageChange(pagination.currentPage + 1)}
              className="flex items-center justify-center w-10 h-10 rounded-md border border-[#285DA6]/30 text-[#285DA6] hover:bg-[#285DA6]/5 hover:border-[#285DA6]/50 transition-colors"
              aria-label="Next page"
            >
              {Icons.chevronRight}
            </button>
          ) : (
            <span
              className="flex items-center justify-center w-10 h-10 rounded-md border border-gray-200 text-gray-300 cursor-not-allowed"
              aria-label="Next page (disabled)"
            >
              {Icons.chevronRight}
            </span>
          )}
        </div>

        <div className="mt-4 text-sm text-gray-500 font-karla">
          Showing page {pagination.currentPage} of {pagination.totalPages}
        </div>
      </div>
    );
  };

  // Helper function to render content based on active section
  const renderContent = () => {
    switch (activeSection) {
      case "dashboard":
        return (
          <AccountDashboard
            userData={userData!}
            bookingsCount={bookings.length}
            upcomingTrips={bookings.filter(booking => new Date(booking.check_in_date) > new Date()).length}
            onEditProfile={() => setActiveSection("profile")}
            onViewTrips={() => setActiveSection("trips")}
          />
        );
      case "trips":
        return (
          <MyTripsTable
            bookings={bookings}
            loading={bookingsLoading}
            onBookingClick={openBookingModal}
            pagination={pagination}
            onPageChange={handlePageChange}
          />
        );
      case "wishlist":
        return (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-gray-400"
              >
                <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Wishlist Coming Soon</h3>
            <p className="text-gray-500">Save your favorite hotels and destinations here.</p>
          </div>
        );
      case "profile":
        return (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Profile Settings</h2>
            {showEditProfile ? (
              <EditProfileForm />
            ) : (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">First Name</label>
                      <p className="text-gray-900 font-medium">{userData?.first_name || "Not provided"}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Email Address</label>
                      <p className="text-gray-900 font-medium">{userData?.email}</p>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Last Name</label>
                      <p className="text-gray-900 font-medium">{userData?.last_name || "Not provided"}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Phone Number</label>
                      <p className="text-gray-900 font-medium">{userData?.phone || "Not provided"}</p>
                    </div>
                  </div>
                </div>
                <div className="pt-6 border-t border-gray-200">
                  <button
                    onClick={() => setShowEditProfile(true)}
                    className="px-4 py-2 bg-[#285DA6] text-white rounded-lg hover:bg-[#1A3A6E] transition-colors font-medium"
                  >
                    Edit Profile
                  </button>
                </div>
              </div>
            )}
          </div>
        );
      case "support":
        return (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-gray-400"
              >
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                <line x1="12" y1="17" x2="12.01" y2="17"></line>
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Need Help?</h3>
            <p className="text-gray-500 mb-6">Contact our support team for assistance with your bookings.</p>
            <a
              href="/contact"
              className="inline-flex items-center px-6 py-3 bg-[#285DA6] text-white rounded-lg hover:bg-[#1A3A6E] transition-colors font-medium"
            >
              Contact Us
            </a>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <AccountLayout
      activeSection={activeSection}
      onSectionChange={setActiveSection}
      userName={userData?.first_name}
      userEmail={userData?.email}
      onLogout={handleLogout}
    >
      {/* Booking Details Modal */}
      <BookingModal />

      {/* Main Content */}
      <div className="section-transition">
        {renderContent()}
      </div>
    </AccountLayout>
  );
};

export default ReactAccountPage;
